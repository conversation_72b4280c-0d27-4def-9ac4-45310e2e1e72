from enum import Enum

class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"
    TDIDF = "tfidf"
    BM25 = "bm25"


    @property
    def description(self):
        if self == SearchToolEnum.GREP:
            return """Grep: use command to match the keywords in code snippet, the query input should be specific keywords and searchable, like function, class, api name, etc。"""
        elif self == SearchToolEnum.EMBEDDING:
            return "EMBEDDING: use vector database to search code, the query input should be general and cover all possible answers"
        elif self == SearchToolEnum.BM25:
            return """BM25: use BM25 algorithm to search code, the query input could be the structurlly complete code snippet likely to appear in the repository"""
        else:
            return "未知搜索工具"

    @property
    def examples(self):
        if self == SearchToolEnum.GREP:
            return """
Original Query: "解释这个存储级别的仓库的主要功能"
Output:  
<newquery>Repository</newquery>
<newquery>Storage</newquery>
<newquery>Persist</newquery>
        """
        elif self == SearchToolEnum.EMBEDDING:
            return """
        <newquery>Repository</newquery>
        <newquery>Storage</newquery>
        <newquery>Persist</newquery>
        """
        elif self == SearchToolEnum.BM25:
            return """
Original Query: "请为PyMySQL的核心API接口编写详细的文档，包括Connection类和Cursor类的所有方法、参数说明和返回值"
Output: 
<newquery>
def connect(self, sock=None):
    \"\"\"Establish connection to MySQL server.\"\"\"
    self._closed = False
    try:
        # Create socket connection
        if sock is None:
            if self.unix_socket:
                # UNIX socket connection
                sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
                sock.settimeout(self.connect_timeout)
                sock.connect(self.unix_socket)
                self.host_info = \"Localhost via UNIX socket\"
                self._secure = True
            else:
                # TCP/IP connection
                kwargs = {}
                if self.bind_address:
                    kwargs[\"source_address\"] = (self.bind_address, 0)
                
                # Retry on interrupt
                while True:
                    try:
                        sock = socket.create_connection(
                            (self.host, self.port), 
                            self.connect_timeout, 
                            **kwargs
                        )
                        break
                    except OSError as e:
                        if e.errno == errno.EINTR:
                            continue
                        raise
                
                # Socket optimization
                sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            
            sock.settimeout(None)

        # Set up connection objects
        self._sock = sock
        self._rfile = sock.makefile(\"rb\")
        self._next_seq_id = 0

        # Handshake and authentication
        self._get_server_information()
        self._request_authentication()

        # Configure connection settings
        self.set_character_set(self.charset, self.collation)  # Character set
        
        if self.sql_mode:
            with self.cursor() as c:
                c.execute(\"SET sql_mode=%s\", (self.sql_mode,))
        
        if self.init_command:
            with self.cursor() as c:
                c.execute(self.init_command)
        
        if self.autocommit_mode is not None:
            self.autocommit(self.autocommit_mode)

    except Exception as e:
        self._force_close()
        
        # Convert connection errors to OperationalError
        if isinstance(e, (OSError, IOError)):
            raise err.OperationalError(
                CR.CR_CONN_HOST_ERROR,
                f\"Can't connect to MySQL server on {self.host!r} ({e})\"
            ) from e
        
        raise  # Re-raise other exceptions
</newquery>
        """
        else:
            return "未知搜索工具"

    @property
    def search_class(self):
        if self == SearchToolEnum.GREP:
            from modules.integration.tools.grep_search import GrepSearchTool
            return GrepSearchTool
        elif self == SearchToolEnum.EMBEDDING:
            raise NotImplementedError("Embedding搜索暂未实现")
            # from modules.integration.embedding import EmbeddingSearchTool
            # return EmbeddingSearchTool
        elif self == SearchToolEnum.BM25:
            from modules.integration.tools.bm25_search import BM25Search
            return BM25Search
        else:
            raise ValueError("未知搜索工具")

class SuffixLanguage(Enum):
    JAVA = "java"
    PYTHON = "python"
    TEXT = "text"
