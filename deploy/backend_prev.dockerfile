FROM python:3.12-slim

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=300 \
    PIP_RETRIES=5


WORKDIR /app

COPY backend/python/ ./

RUN ls -al

# 1. 先安装基础依赖
RUN pip install --no-cache-dir \
    setuptools>=61.0 wheel

# 2. 安装项目依赖
RUN pip install -r requirements.txt

# 创建非 root 用户
# 创建 /export/Logs 目录并设置权限
RUN mkdir -p /export/Logs && \
    useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app && \
    chown -R app:app /export/Logs
USER app

EXPOSE 8080

# 定义容器启动命令
CMD ["python", "main.py", "--env", "prev"]
