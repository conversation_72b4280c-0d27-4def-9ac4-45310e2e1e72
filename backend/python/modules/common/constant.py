from enum import Enum

class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"
    TDIDF = "tfidf"
    BM25 = "bm25"


    @property
    def description(self):
        if self == SearchToolEnum.GREP:
            return """Grep: use command to match the keywords in code snippet, the query input should be specific keywords and searchable。
- Expected Query Features
1. keyword that likely appear in code snippets, like function, class, api name, etc 
2. keyword can be directly found in the code or documents
3. Just only one word

- Examples
Original Query: "解释这个存储级别的仓库的主要功能"
Output:  
<subquery>Repository</subquery>
<subquery>Storage</subquery>
<subquery>Persist</subquery>
"""
        elif self == SearchToolEnum.EMBEDDING:
            return "embedding: use vector database to search code, the query input should be general and cover all possible answers"
        elif self == SearchToolEnum.BM25:
            return """BM25: use BM25 algorithm to search code, the query input could be the structurlly complete code snippet likely to appear in the repository

- Examples
Original Query: "请为PyMySQL的核心API接口编写详细的文档，包括Connection类和Cursor类的所有方法、参数说明和返回值"
Output: 
<subquery>
class Connection:
    \"\"\"Representation of a socket with a mysql server.\"\"\"
    
    # Class variables
    _sock = None
    _rfile = None
    _auth_plugin_name = \"\"
    _closed = False
    _secure = False

    def __init__(self, **kwargs):
        \"\"\"Initialize connection with various MySQL parameters.\"\"\"
        # Parameter processing and validation
        # SSL configuration
        # Connection initialization

    def __enter__(self):
        \"\"\"Context manager entry.\"\"\"
        return self

    def __exit__(self, *exc_info):
        \"\"\"Context manager exit.\"\"\"
        self.close()

    def _create_ssl_ctx(self, sslp):
        \"\"\"Create SSL context for secure connection.\"\"\"

    def close(self):
        \"\"\"Close the connection.\"\"\"

    @property
    def open(self):
        \"\"\"Return True if connection is open.\"\"\"

    def _force_close(self):
        \"\"\"Force close without QUIT message.\"\"\"

    def autocommit(self, value):
        \"\"\"Set autocommit mode.\"\"\"

    def get_autocommit(self):
        \"\"\"Get current autocommit status.\"\"\"

    def _read_ok_packet(self):
        \"\"\"Read OK packet from server.\"\"\"

    def _send_autocommit_mode(self):
        \"\"\"Send autocommit setting to server.\"\"\"

    def begin(self):
        \"\"\"Begin transaction.\"\"\"

    def commit(self):
        \"\"\"Commit transaction.\"\"\"

    def rollback(self):
        \"\"\"Rollback transaction.\"\"\"

    def show_warnings(self):
        \"\"\"Show server warnings.\"\"\"

    def select_db(self, db):
        \"\"\"Select database.\"\"\"

    def escape(self, obj, mapping=None):
        \"\"\"Escape value for SQL.\"\"\"

    def literal(self, obj):
        \"\"\"Alias for escape().\"\"\"

    def escape_string(self, s):
        \"\"\"Escape string for SQL.\"\"\"

    def _quote_bytes(self, s):
        \"\"\"Escape bytes for SQL.\"\"\"

    def cursor(self, cursor=None):
        \"\"\"Create new cursor.\"\"\"

    # Internal methods for query execution
    def query(self, sql, unbuffered=False):
        \"\"\"Execute SQL query.\"\"\"

    def next_result(self, unbuffered=False):
        \"\"\"Get next result set.\"\"\"

    def affected_rows(self):
        \"\"\"Get affected rows count.\"\"\"

    def kill(self, thread_id):
        \"\"\"Kill server thread.\"\"\"

    def ping(self, reconnect=True):
        \"\"\"Check server connection.\"\"\"

    def set_charset(self, charset):
        \"\"\"Set connection charset (deprecated).\"\"\"

    def set_character_set(self, charset, collation=None):
        \"\"\"Set character set and collation.\"\"\"

    def connect(self, sock=None):
        \"\"\"Establish connection to server.\"\"\"

    def write_packet(self, payload):
        \"\"\"Write packet to server.\"\"\"

    def _read_packet(self, packet_type=MysqlPacket):
        \"\"\"Read packet from server.\"\"\"

    def _read_bytes(self, num_bytes):
        \"\"\"Read bytes from socket.\"\"\"

    def _write_bytes(self, data):
        \"\"\"Write bytes to socket.\"\"\"

    def _read_query_result(self, unbuffered=False):
        \"\"\"Read query results.\"\"\"

    def insert_id(self):
        \"\"\"Get last insert ID.\"\"\"

    def _execute_command(self, command, sql):
        \"\"\"Execute command with SQL.\"\"\"

    def _request_authentication(self):
        \"\"\"Handle authentication with server.\"\"\"

    def _process_auth(self, plugin_name, auth_packet):
        \"\"\"Process authentication plugin.\"\"\"

    def _get_auth_plugin_handler(self, plugin_name):
        \"\"\"Get handler for auth plugin.\"\"\"

    # Compatibility methods
    def thread_id(self):
        \"\"\"Get server thread ID.\"\"\"

    def character_set_name(self):
        \"\"\"Get current charset name.\"\"\"

    def get_host_info(self):
        \"\"\"Get host connection info.\"\"\"

    def get_proto_info(self):
        \"\"\"Get protocol version.\"\"\"

    def _get_server_information(self):
        \"\"\"Get server version and capabilities.\"\"\"

    def get_server_info(self):
        \"\"\"Get server version string.\"\"\"

    # Error classes
    Warning = err.Warning
    Error = err.Error
    InterfaceError = err.InterfaceError
    DatabaseError = err.DatabaseError
    DataError = err.DataError
    OperationalError = err.OperationalError 
    IntegrityError = err.IntegrityError
    InternalError = err.InternalError
    ProgrammingError = err.ProgrammingError
    NotSupportedError = err.NotSupportedError


class MySQLResult:
    \"\"\"Handles MySQL result sets.\"\"\"
    
    def __init__(self, connection):
        \"\"\"Initialize result handler.\"\"\"

    def __del__(self):
        \"\"\"Clean up unbuffered queries.\"\"\"

    def read(self):
        \"\"\"Read entire result set.\"\"\"

    def init_unbuffered_query(self):
        \"\"\"Initialize unbuffered query.\"\"\"

    def _read_ok_packet(self, first_packet):
        \"\"\"Read OK packet.\"\"\"

    def _read_load_local_packet(self, first_packet):
        \"\"\"Handle LOAD LOCAL INFILE.\"\"\"

    def _check_packet_is_eof(self, packet):
        \"\"\"Check if packet is EOF.\"\"\"

    def _read_result_packet(self, first_packet):
        \"\"\"Read result metadata.\"\"\"

    def _read_rowdata_packet_unbuffered(self):
        \"\"\"Read row data (unbuffered).\"\"\"

    def _finish_unbuffered_query(self):
        \"\"\"Complete unbuffered query.\"\"\"

    def _read_rowdata_packet(self):
        \"\"\"Read row data (buffered).\"\"\"

    def _read_row_from_packet(self, packet):
        \"\"\"Read single row from packet.\"\"\"

    def _get_descriptions(self):
        \"\"\"Get column descriptions.\"\"\"


class LoadLocalFile:
    \"\"\"Handles LOAD LOCAL INFILE operations.\"\"\"
    
    def __init__(self, filename, connection):
        \"\"\"Initialize file loader.\"\"\"

    def send_data(self):
        \"\"\"Send file data to server.\"\"\"
</subquery>

<subquery>
def connect(self, sock=None):
    \"\"\"Establish connection to MySQL server.\"\"\"
    self._closed = False
    try:
        # Create socket connection
        if sock is None:
            if self.unix_socket:
                # UNIX socket connection
                sock = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
                sock.settimeout(self.connect_timeout)
                sock.connect(self.unix_socket)
                self.host_info = \"Localhost via UNIX socket\"
                self._secure = True
            else:
                # TCP/IP connection
                kwargs = {}
                if self.bind_address:
                    kwargs[\"source_address\"] = (self.bind_address, 0)
                
                # Retry on interrupt
                while True:
                    try:
                        sock = socket.create_connection(
                            (self.host, self.port), 
                            self.connect_timeout, 
                            **kwargs
                        )
                        break
                    except OSError as e:
                        if e.errno == errno.EINTR:
                            continue
                        raise
                
                # Socket optimization
                sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            
            sock.settimeout(None)

        # Set up connection objects
        self._sock = sock
        self._rfile = sock.makefile(\"rb\")
        self._next_seq_id = 0

        # Handshake and authentication
        self._get_server_information()
        self._request_authentication()

        # Configure connection settings
        self.set_character_set(self.charset, self.collation)  # Character set
        
        if self.sql_mode:
            with self.cursor() as c:
                c.execute(\"SET sql_mode=%s\", (self.sql_mode,))
        
        if self.init_command:
            with self.cursor() as c:
                c.execute(self.init_command)
        
        if self.autocommit_mode is not None:
            self.autocommit(self.autocommit_mode)

    except Exception as e:
        self._force_close()
        
        # Convert connection errors to OperationalError
        if isinstance(e, (OSError, IOError)):
            raise err.OperationalError(
                CR.CR_CONN_HOST_ERROR,
                f\"Can't connect to MySQL server on {self.host!r} ({e})\"
            ) from e
        
        raise  # Re-raise other exceptions
</subquery>
"""
        else:
            return "未知搜索工具"

    @property
    def search_class(self):
        if self == SearchToolEnum.GREP:
            from modules.integration.tools.grep_search import GrepSearchTool
            return GrepSearchTool
        elif self == SearchToolEnum.EMBEDDING:
            raise NotImplementedError("Embedding搜索暂未实现")
            # from modules.integration.embedding import EmbeddingSearchTool
            # return EmbeddingSearchTool
        elif self == SearchToolEnum.BM25:
            from modules.integration.tools.bm25_search import BM25Search
            return BM25Search
        else:
            raise ValueError("未知搜索工具")

class SuffixLanguage(Enum):
    JAVA = "java"
    PYTHON = "python"
    TEXT = "text"
