"""
LLM客户端模块
提供统一的大模型调用接口
"""

import json
import httpx
from typing import AsyncGenerator, List, Dict

from core.config import get_config
from utils.logger import logger

class LLMClient:
    """LLM调用客户端"""
    
    def __init__(self):
        """
        初始化LLM客户端
        """
        self.config = get_config().llm
        
    
    def _get_headers(self) -> Dict:
        return {
            "Authorization": f"{self.config.api_key}",
            "Content-Type": "application/json"
        }
    
    def _get_request(self, prompt: str, system_prompt: str) -> Dict:
        return {
            "model": self.config.model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            "stream": self.config.stream,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens
        }
        
    def _call_llm_stream(self, prompt: str, system_prompt: str) -> str:
        """
        流式调用LLM生成回复

        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词

        Returns:
            str: LLM生成的完整回复
        """
        request_data = self._get_request(prompt, system_prompt)
        headers = self._get_headers()

        # logger.info(f"LLM Stream Service: Header: {headers} Request data: {request_data}")

        content = ""
        try:
            with httpx.stream("POST", self.config.base_url, json=request_data, headers=headers) as response:
                # 检查响应状态
                if response.status_code != 200:
                    logger.error(f"LLM流式调用失败，状态码: {response.status_code}, 响应: {response.text}")
                    return ""

                # 逐行读取流式响应
                for line in response.iter_lines():
                    if not line.strip():
                        continue

                    # 处理SSE格式的数据
                    if line.startswith("data: "):
                        data_content = line[6:]  # 移除 "data: " 前缀

                        # 检查是否为结束标志
                        if data_content.strip() == "[DONE]":
                            break

                        try:
                            # 解析JSON数据
                            chunk_data = json.loads(data_content)

                            # 提取内容
                            if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                choice = chunk_data['choices'][0]
                                if 'delta' in choice and 'content' in choice['delta']:
                                    chunk_content = choice['delta']['content']
                                    if chunk_content:
                                        content += chunk_content

                        except json.JSONDecodeError as e:
                            logger.warning(f"解析流式响应JSON失败: {e}, 数据: {data_content}")
                            continue
                        except Exception as e:
                            logger.warning(f"处理流式响应块失败: {e}")
                            continue

        except Exception as e:
            logger.error(f"LLM流式调用失败: {e}")
            return ""

        return content


    def _call_llm_raw(self, prompt: str, system_prompt: str) -> str:
        request_data = self._get_request(prompt, system_prompt)
        headers = self._get_headers()

        # logger.info(f"LLM Service: Header: {headers} Request data: {request_data}")

        try:
            reponse = httpx.post(self.config.base_url, json=request_data, headers=headers)
            result = json.loads(reponse.read().decode('utf-8'))

            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                return content
            else:
                logger.info(f"No choices in response: {result}")
                return ""
            
        except Exception as e:
            logger.info(f"LLM调用失败: {e}")
            return ""
        
    def call(self, prompt: str, system_prompt: str = "你是一个专业的代码分析助手") -> str:
        """
        调用LLM生成回复
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            
        Returns:
            str: LLM生成的回复
        """
        try:
            if self.config.stream:
                return self._call_llm_stream(prompt, system_prompt)
            else:
                return self._call_llm_raw(prompt, system_prompt)
                
        except Exception as e:
            print(f"LLM调用失败: {e}")
            return ""
    
    def call_json(self, prompt: str, system_prompt: str = "你是一个专业的代码分析助手") -> List[str]:
        """
        调用LLM并解析JSON格式回复
        
        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            
        Returns:
            List[str]: 解析后的列表，解析失败返回空列表
        """
        response = self.call(prompt, system_prompt)
        if not response:
            return []
            
        try:
            # 尝试直接解析JSON
            result = json.loads(response)
            if isinstance(result, list):
                return result
            else:
                return []
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试提取JSON部分
            try:
                # 查找可能的JSON数组
                start_idx = response.find('[')
                end_idx = response.rfind(']')
                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    json_str = response[start_idx:end_idx + 1]
                    result = json.loads(json_str)
                    if isinstance(result, list):
                        return result
            except:
                pass
            
            print(f"JSON解析失败，原始回复: {response}")
            return []
    
    def call_structure(self, prompt: str, system_prompt: str = "你是一个专业的代码分析助手", structure_keyword: str = None) -> List[str]:
        """
        调用LLM并解析结构化回复

        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            structure_keyword: 结构化关键字

        Raises:
            ValueError: 如果结构化关键字为空

        Notes:
            1. 结构化关键字用于标识回复的开始和结束
            2. 回复内容在结构化关键字之间
            3. 结构化关键字可以是任意字符串，如<subquery>和</subquery>
            4. 回复内容可以包含多个结构化关键字
        Returns:
            str: 解析后的结构化回复
        """
        if not structure_keyword:
            raise ValueError("结构化关键字不能为空")

        # 调用LLM获取原始回复
        response = self.call(prompt, system_prompt)
        logger.debug(f"LLM回复: {response}")
        if not response:
            return ""

        # 构建开始和结束标签
        start_tag = f"<{structure_keyword}>"
        end_tag = f"</{structure_keyword}>"

        # 提取所有结构化内容
        structured_contents = []
        current_pos = 0

        while True:
            # 查找开始标签
            start_idx = response.find(start_tag, current_pos)
            if start_idx == -1:
                break

            # 查找对应的结束标签
            end_idx = response.find(end_tag, start_idx + len(start_tag))
            if end_idx == -1:
                # 如果没有找到结束标签，记录警告并跳过
                logger.warning(f"未找到结构化关键字 '{structure_keyword}' 的结束标签")
                break

            # 提取标签之间的内容
            content = response[start_idx + len(start_tag):end_idx].strip()
            if content:
                structured_contents.append(content)

            # 更新搜索位置
            current_pos = end_idx + len(end_tag)

        # 如果没有找到任何结构化内容，返回空字符串
        if not structured_contents:
            logger.warning(f"在LLM回复中未找到结构化关键字 '{structure_keyword}' 的内容")
            return ""

        # 将多个结构化内容用换行符连接
        return structured_contents

    def _decode_stream_response(self, response) -> str:
        """
        解码流式响应
        
        Args:
            response: 流式响应对象
            
        Returns:
            str: 解码后的内容
        """
        content = ""
        response_str = ""
        try:
            response_str = response.model_dump_json()
            response_obj = json.loads(response_str)

            # 检查响应结构的完整性
            if not response_obj or "choices" not in response_obj:
                return ""

            choices = response_obj["choices"]
            if not choices or len(choices) == 0:
                return ""

            first_choice = choices[0]
            if not first_choice or "delta" not in first_choice:
                return ""

            delta = first_choice["delta"]
            if not delta or "content" not in delta:
                return ""

            content = delta["content"]
        except Exception as e:
            if "DONE" in response_str:
                return ""
            print(f"解析流式响应失败: {e}, 响应: {response_str}")
        
        if content is None:
            return ""
        if content == '"':
            return '"""'
        return content


# 创建默认的LLM客户端实例
default_llm_client = LLMClient()
